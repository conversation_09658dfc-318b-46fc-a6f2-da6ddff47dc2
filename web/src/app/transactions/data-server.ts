import "server-only";

import {
  PortalPurchaseRelations,
  PortalPurchaseResponse,
  PortalRetirementRelations,
  PortalRetirementResponse,
  PortalTransactionQuery,
  PortalTransactionQueryResponse,
  TransactionType,
  uuid,
} from "@rubiconcarbon/shared-types";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { ssrFetch } from "../data-server";
import { generateQueryParams } from "@app/utilities/fetch";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";

export const getTransactions = async (
  queryParams: PortalTransactionQuery = {},
): Promise<MaybeNothing<PortalTransactionQueryResponse>> =>
  ssrFetch(
    `transactions?${generateQueryParams({
      limit: DEFAULT_MAX_PAGING_LIMIT,
      includeTotalCount: true,
      ...queryParams,
    })}`,
  );

export const getPortfolioTransactions = async (
  queryParams: PortalTransactionQuery = {},
): Promise<MaybeNothing<PortalTransactionQueryResponse>> => getTransactions(queryParams);

export const getTransaction = async (
  type: Extract<TransactionType, TransactionType.PURCHASE | TransactionType.RETIREMENT>,
  id: uuid,
  includeRelations: (PortalPurchaseRelations | PortalRetirementRelations)[] = [],
): Promise<MaybeNothing<PortalPurchaseResponse | PortalRetirementResponse>> =>
  ssrFetch(
    `${type}s/${id}?${generateQueryParams({
      includeRelations,
    })}`,
  );

export { getOrganizations } from "../data-server";

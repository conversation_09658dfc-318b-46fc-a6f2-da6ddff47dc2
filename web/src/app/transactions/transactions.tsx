"use client";

import { DataTable, customGridDefs } from "@app/components/data-table";
import Grid from "@mui/material/Grid";
import {
  GridColDef,
  GridRenderCellParams,
  GridSortDirection,
  GridSortItem,
  GridSortModel,
  GridValueGetterParams,
  NoRowsOverlayPropsOverrides,
} from "@mui/x-data-grid";
import {
  RetirementType,
  PortalTransactionQuery,
  PortalTransactionQueryResponse,
  PortalTransactionResponse,
  TransactionType,
  uuid,
  AssetType,
} from "@rubiconcarbon/shared-types";
import { ChangeEvent, MouseEvent, useContext, useMemo, useState } from "react";
import PageContentHeading from "../page-components/heading";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import StatusChip from "@app/components/styled/status-chip";
import Decimal from "decimal.js";
import { nowAsString, utcDateFormat } from "@app/utilities/date";
import { BLANK, DEFAULT_MAX_PAGING_LIMIT, THREE_HUNDRED, ZERO } from "@app/constants/common";
import { PortalUpdaterContextProvider } from "../portal-updater-context";
import { TransactionStatus, TransactionStatusTypeName, TransactionTypeName } from "@app/constants/transactions";
import {
  currencyFormat,
  GenericTabItem,
  GenericTabKey,
  GenericTabs,
  Maybe,
  MaybeNothing,
  numberFormat,
  pickFromArrayOfRecords,
  px,
  searchByKeys,
  Undefinable,
  useKeepWhileValidate,
} from "@rubiconcarbon/frontend-shared";
import { PaginationModel } from "@app/types/table";
import { TABLE_GRID_FEATURE_MODE } from "@app/constants/table";
import { useDebounce, useUpdateEffect } from "react-use";
import { useLogger } from "@app/providers/logging-provider";
import useTriggerRequest from "@app/hooks/useTriggerRequest";
import Link from "next/link";
import { Typography } from "@mui/material";
import ProductName from "@app/components/styled/product-name";
import { getProductName } from "@app/utilities/get-product-name";
import ContentContainer from "../components/content-container/content-container";
import HeadingContainer from "../components/heading-container/heading-container";
import { StoreContext } from "@app/providers/state/store";
import { toNumber } from "lodash";

import classes from "./styles/transactions.module.scss";

type ServerSortKey = keyof typeof ServerSortMap;

type Tab = "tranasactions" | "forwards";

type ContentProps = {
  type: Tab;
  hasForwardTransactions: boolean | undefined;
  loading: boolean;
  transactions: MaybeNothing<PortalTransactionQueryResponse>;
  onSort: (field: ServerSortKey, sort: GridSortDirection) => void;
  onPage: (page: number, pageSize: number) => void;
};

type TransactionsProps = {
  serverTransactions: MaybeNothing<PortalTransactionQueryResponse>;
};

type TransactionRowModel = {
  id: uuid;
  uiKey: string;
  createdAt: string;
  type: TransactionType;
  assets: string;
  amount: number;
  totalPrice: Decimal;
  status: TransactionStatus;
  deliveryDate: string;
};

const tabs: GenericTabItem[] = [
  {
    key: "tranasactions",
    data: {
      key: "Transactions",
      class: "data-tour-step-transactions", // for tour if we add this data point
    },
  },
  {
    key: "forwards",
    data: {
      key: "Forward Delivery",
      class: "data-tour-step-forwards", // for tour if we add this data point
    },
  },
];

const { small, medium, large } = customGridDefs;

const isSellTransaction = (transaction: PortalTransactionResponse): boolean =>
  // forward line items are only suppose to be sell transactions in portal
  [TransactionType.PURCHASE, TransactionType.FORWARD_LINE_ITEM].includes(transaction.type);

const Columns: GridColDef[] = [
  {
    field: "uiKey",
    headerName: "Key",
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>): string => row?.uiKey,
    ...medium,
  },
  {
    field: "createdAt",
    headerName: "Date",
    sortComparator: (a: string, b: string): number => {
      const date1 = new Date(a).getTime();
      const date2 = new Date(b).getTime();
      return date1 - date2;
    },
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>): string =>
      utcDateFormat(row?.createdAt?.toString(), { defaultValue: BLANK }),
    ...small,
  },
  {
    field: "type",
    headerName: "Type",
    type: "singleSelect",
    valueOptions: [
      { value: TransactionType.PURCHASE, label: TransactionTypeName.PURCHASE },
      { value: TransactionType.RETIREMENT, label: TransactionTypeName.RETIREMENT },
      { value: RetirementType.TRANSFER_OUTFLOW, label: TransactionTypeName.TRANSFER_OUTFLOW },
      { value: TransactionType.FORWARD_LINE_ITEM, label: TransactionTypeName.FORWARD_LINE_ITEM },
    ],
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>): string =>
      row?.subtype === RetirementType.TRANSFER_OUTFLOW ? row?.subtype : row?.type,
    ...small,
  },
  {
    field: "assets",
    headerName: "Product",
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>) => getProductName(row),
    renderCell: ({ row }: GridRenderCellParams<PortalTransactionResponse>): JSX.Element => (
      <ProductName transaction={row} />
    ),
    ...large,
  },
  {
    field: "amount",
    headerName: "Quantity",
    type: "number",
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>): number => row?.totalQuantity,
    renderCell: ({ row }: GridRenderCellParams<PortalTransactionResponse>) =>
      numberFormat(row?.totalQuantity ?? 0, { separator: "thousand" }),
    ...small,
  },
  {
    field: "totalPrice",
    headerName: "Total Amount",
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>) =>
      currencyFormat(isSellTransaction(row) ? toNumber(row?.totalPrice) : BLANK),
    ...small,
  },
  {
    field: "deliveryDate",
    headerName: "Delivery Date",
    sortComparator: (a: string, b: string): number => {
      const date1 = new Date(a).getTime();
      const date2 = new Date(b).getTime();
      return date1 - date2;
    },
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>): string =>
      utcDateFormat(row?.assetFlows?.at(0)?.lastUpdatedDeliveryDate?.toString(), { defaultValue: BLANK }),
    ...small,
  },
  {
    field: "status",
    headerName: "Status",
    type: "singleSelect",
    valueOptions: [
      { value: TransactionStatus.INDICATIVE, label: TransactionStatusTypeName.INDICATIVE },
      { value: TransactionStatus.FIRM, label: TransactionStatusTypeName.FIRM },
      { value: TransactionStatus.BINDING, label: TransactionStatusTypeName.BINDING },
      { value: TransactionStatus.EXECUTED, label: TransactionStatusTypeName.EXECUTED },
      { value: TransactionStatus.PAID, label: TransactionStatusTypeName.PAID },
      { value: TransactionStatus.DELIVERED, label: TransactionStatusTypeName.DELIVERED },
      { value: TransactionStatus.SETTLED, label: TransactionStatusTypeName.SETTLED },
      { value: TransactionStatus.COMPLETED, label: TransactionStatusTypeName.COMPLETED },
      { value: TransactionStatus.CANCELED, label: TransactionStatusTypeName.CANCELED },
      { value: TransactionStatus.PROCESSING, label: TransactionStatusTypeName.PROCESSING },
      { value: TransactionStatus.ADMIN_REVIEW, label: TransactionStatusTypeName.ADMIN_REVIEW },
      { value: TransactionStatus.PORTFOLIO_MANAGER_REVIEW, label: TransactionStatusTypeName.PORTFOLIO_MANAGER_REVIEW },
      { value: TransactionStatus.FAILED, label: TransactionStatusTypeName.FAILED },
      { value: TransactionStatus.REVERTED, label: TransactionStatusTypeName.REVERTED },
      { value: TransactionStatus.PENDING, label: TransactionStatusTypeName.PENDING },
    ],
    valueGetter: ({ row }: GridValueGetterParams<PortalTransactionResponse>): string => row?.status,
    renderCell: ({ value }: GridRenderCellParams<PortalTransactionResponse, string>) =>
      value && <StatusChip status={value} />,
    ...small,
  },
  {
    field: "Details",
    headerName: "",
    sortable: false,
    filterable: false,
    hideable: false,
    disableExport: true,
    renderCell: ({ row }: GridRenderCellParams<PortalTransactionResponse>): JSX.Element => (
      <Link href={`/transactions/${row?.id}?type=${row?.type}` as any}>
        <Typography>View details</Typography>
      </Link>
    ),
    ...medium,
    align: "center",
  },
];

const defaultSortModel: GridSortItem = { field: "createdAt", sort: "desc" };

const ServerSortMap = {
  createdAt: "createdAt",
};

const toModel = (row: PortalTransactionResponse): TransactionRowModel => ({
  id: row?.id,
  uiKey: (Columns?.find(({ field }) => field === "uiKey") as any)?.valueGetter({ row } as any) || "",
  createdAt: (Columns?.find(({ field }) => field === "createdAt") as any)?.valueGetter({ row } as any) || "",
  type: (Columns?.find(({ field }) => field === "type") as any)?.valueGetter({ row } as any) || "",
  assets: (Columns?.find(({ field }) => field === "assets") as any)?.valueGetter({ row } as any) || "",
  amount: (Columns?.find(({ field }) => field === "amount") as any)?.valueGetter({ row } as any) || "",
  totalPrice: (Columns?.find(({ field }) => field === "totalPrice") as any)?.valueGetter({ row } as any) || "",
  status: (Columns?.find(({ field }) => field === "status") as any)?.valueGetter({ row } as any) || "",
  deliveryDate: (Columns?.find(({ field }) => field === "deliveryDate") as any)?.valueGetter({ row } as any) || "",
});

const COMMON_COLUMNS = ["uiKey", "createdAt", "type", "assets", "amount", "totalPrice", "status"];
const TRANSACTION_COLUMNS = ["Details"];
const FORWARD_COLUMNS = ["deliveryDate"];

const Content = ({ type, hasForwardTransactions, loading, transactions }: ContentProps): JSX.Element => {
  const { ephemeralState } = useContext(StoreContext);

  const [sortModel, setSortModel] = useState<GridSortModel>([defaultSortModel]);
  const [paginationModel, setPaginationModel] = useState<PaginationModel>({ page: 0, pageSize: 100 });
  const [searchTerm, setSearchTerm] = useState<string>();
  const [filteredIds, setFilteredIds] = useState<uuid[]>();
  const [debounceMS, setDebounceMS] = useState<number>(THREE_HUNDRED);

  const pagedRows = useMemo(
    () =>
      transactions?.data?.filter((transaction) =>
        type === "forwards"
          ? transaction.type === TransactionType.FORWARD_LINE_ITEM
          : transaction.type !== TransactionType.FORWARD_LINE_ITEM,
      ) || [],
    [transactions?.data, type],
  );

  // commented out for now as server api is insufficient for filtering and export
  // const removedTypelessTransactionCount = useMemo(
  //   () =>
  //     (
  //       transactions?.data || []
  //     )?.length,
  //   [transactions?.data],
  // );
  // const rowCount = useMemo(
  //   () => (transactions?.page?.totalCount || 0) - removedTypelessTransactionCount,
  //   [removedTypelessTransactionCount, transactions?.page?.totalCount],
  // );

  const tableRows = useMemo(() => {
    if (!!filteredIds) return pagedRows?.filter(({ id }) => filteredIds.includes(id));
    return pagedRows;
  }, [filteredIds, pagedRows]);

  useDebounce(
    () => {
      if (searchTerm !== undefined) {
        const ids = pickFromArrayOfRecords(
          searchByKeys(searchTerm, pagedRows?.map(toModel), [
            "uiKey",
            "createdAt",
            "type",
            "assets",
            "totalPrice",
            "amount",
            "status",
            "deliveryDate",
          ]),
          ["id"],
        ).reduce((ids: uuid[], { id }) => [...ids, id] as uuid[], []);

        if (pagedRows?.length === ids?.length) setFilteredIds(undefined);
        else setFilteredIds(ids);
      }
    },
    debounceMS,
    [searchTerm, pagedRows, paginationModel],
  );

  useUpdateEffect(() => {
    if (loading) setDebounceMS(0);
    else setTimeout(() => setDebounceMS(THREE_HUNDRED), THREE_HUNDRED);
  }, [loading]);

  const handleSearch = (event: ChangeEvent<HTMLInputElement>): void => {
    event?.preventDefault();
    setSearchTerm(event?.currentTarget?.value);
  };

  const handleSearchClear = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();
    setSearchTerm("");
  };

  return (
    <Grid
      item
      xs={12}
      container
      direction="row"
      sx={{
        position: "relative",
        flexGrow: "100%",
        height: `calc(100vh - ${!!hasForwardTransactions ? "210" : "155"}px)`,
        ">*": { pt: "10px" },
      }}
    >
      <DataTable
        dataGridProps={{
          "aria-label": "User transactions data grid",
          columns: Columns?.filter(({ field }) =>
            type === "forwards"
              ? [...COMMON_COLUMNS, ...FORWARD_COLUMNS].includes(field)
              : [...COMMON_COLUMNS, ...TRANSACTION_COLUMNS].includes(field),
          ),
          rows: tableRows,
          columnBuffer: 9,
          components: {
            NoRowsOverlay,
          },
          autoHeight: false,
          slotProps: {
            noRowsOverlay: {
              message: "When you complete a transaction with Rubicon Carbon, it will appear here.",
            } as NoRowsOverlayPropsOverrides,
            toolbar: {
              searchOption: {
                value: searchTerm,
                onSearch: handleSearch,
                onClear: handleSearchClear,
              },
              ...px(
                {
                  csvOptions: tableRows?.length > 0 && {
                    allColumns: false,
                    fileName: `${ephemeralState?.organization?.name}_${nowAsString()}_${type === "forwards" ? "Forward_Deliveries" : "Transactions"}`,
                  },
                },
                [false],
              ),
            },
          },
          sortingMode: TABLE_GRID_FEATURE_MODE.client, // using client for now as server api is insufficient for filtering and export
          paginationMode: TABLE_GRID_FEATURE_MODE.client, // using client for now as server api is insufficient for filtering and export
          sortModel,
          paginationModel,
          onSortModelChange: setSortModel,
          onPaginationModelChange: setPaginationModel,
        }}
      />
    </Grid>
  );
};

export default function Transactions({ serverTransactions }: TransactionsProps): JSX.Element {
  const { logger } = useLogger();

  const [activeTab, setActiveTab] = useState<Tab>("tranasactions");
  const [offset, setOffset] = useState<number>(ZERO);
  const [limit, setLimit] = useState<number>(DEFAULT_MAX_PAGING_LIMIT);
  const [orderBy, setOrderBy] = useState<MaybeNothing<string>>("createdAt");
  const [orderByDirection, setOrderByDirection] =
    useState<MaybeNothing<"asc_nulls_last" | "desc_nulls_last">>("desc_nulls_last");

  const {
    data: clientTransactions,
    trigger: updater,
    isMutating: loading,
  } = useTriggerRequest<PortalTransactionQueryResponse, null, null, PortalTransactionQuery>({
    url: `/transactions`,
    queryParams: {
      offset,
      limit,
      includeTotalCount: true,
      types: [TransactionType.PURCHASE, TransactionType.RETIREMENT, TransactionType.FORWARD_LINE_ITEM],
      ...px({ orderBy, orderByDirection }, [null, undefined]),
    },
    swrOptions: {
      onSuccess: (data: Undefinable<PortalTransactionQueryResponse>): void => {
        updateValue(data);
        stopValidating();
      },
      onError: (error: any) => {
        stopValidating();
        logger.error(error?.message, {});
      },
    },
  });

  const {
    value: refreshedClientTransactions,
    updateValue,
    startValidating,
    stopValidating,
  } = useKeepWhileValidate(clientTransactions, loading);
  const response = refreshedClientTransactions ?? serverTransactions!;
  const refinedTransactions = response?.data?.map(({ type, ...rest }) => {
    if (type === TransactionType.RETIREMENT && rest?.assetFlows?.some((flow) => flow?.asset?.type === AssetType.RCT)) {
      const assetFlows = rest.assetFlows.filter((flow) => flow.asset.type === AssetType.RCT);

      return {
        type,
        ...rest,
        totalQuantity: assetFlows.at(0)!.amount,
        assetFlows,
      };
    }

    return {
      type,
      ...rest,
    };
  });

  const transactions = {
    data: refinedTransactions,
    page: response.page,
  };

  const hasForwardTransactions = useMemo(
    () => transactions?.data?.some(({ type }) => type === TransactionType.FORWARD_LINE_ITEM),
    [transactions?.data],
  );

  // commented out for now as server api is insufficient for filtering and export
  // useEffect(() => setLimit(transactions?.page.totalCount), [transactions?.page.totalCount]);

  // const { data: clientTransactionsResponse, mutate: updater } = useSWR<Maybe<TransactionQueryResponse>>(
  //   `books/${id}/transactions`,
  //   { revalidateOnFocus: false, revalidateOnMount: false },
  // );

  const renderTab = (tab: any): JSX.Element => <>{tab?.key}</>;

  const handleSortChange = (field: ServerSortKey, sort: GridSortDirection): void => {
    if (!!sort) {
      setOrderBy(ServerSortMap[field]);
      setOrderByDirection(`${sort}_nulls_last`);
    } else {
      setOrderBy(null);
      setOrderByDirection(null);
    }

    setTimeout(async () => {
      startValidating();
      await updater();
    });
  };

  const handlePageChange = (page: number, pageSize: number): void => {
    const {
      page: { size },
    } = transactions || { page: {} };
    const potentialOffset = page ? page * (size ?? 0) - 1 : 0;

    if (potentialOffset !== offset || pageSize !== limit) {
      if (potentialOffset !== offset) setOffset(potentialOffset);
      if (pageSize !== limit) setLimit(pageSize);

      setTimeout(async () => {
        startValidating();
        await updater();
      });
    }
  };

  return (
    <>
      <HeadingContainer>
        <PortalUpdaterContextProvider updater={updater}>
          <PageContentHeading
            headingId="transactions-page-heading"
            headingText="Transactions"
            buttons={{ buy: true, retire: true }}
          />
        </PortalUpdaterContextProvider>
      </HeadingContainer>
      <ContentContainer>
        <Maybe condition={!!hasForwardTransactions}>
          <GenericTabs
            tabs={tabs}
            value={activeTab}
            renderTab={renderTab}
            classes={{
              root: classes.Tabs,
              tab: (data: any) => `${classes.Tab} ${!!data?.class ? ` ${data?.class}` : ""}`,
              active: classes.TabSelected,
            }}
            onTabChange={(key: GenericTabKey): void => setActiveTab(key as Tab)}
          />
        </Maybe>
        <Content
          {...{
            type: activeTab,
            hasForwardTransactions,
            loading,
            transactions,
            onSort: handleSortChange,
            onPage: handlePageChange,
          }}
        />
      </ContentContainer>
    </>
  );
}

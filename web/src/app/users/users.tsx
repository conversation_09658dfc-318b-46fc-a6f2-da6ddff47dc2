"use client";

import { Maybe, OrganizationUserRole, PortalUserQueryResponse, PortalUserResponse } from "@rubiconcarbon/shared-types";
import PageContentHeading from "../page-components/heading";
import Grid from "@mui/material/Grid";
import { DataTable, customGridDefs } from "@app/components/data-table";
import { GridColDef, GridRenderCellParams, GridValueGetterParams } from "@mui/x-data-grid";
import { UserStatus, OrganizationUserRoleName } from "@app/constants/users";
import UserStatusRenderer from "@app/components/styled/user-status-renderer";
import { useMemo } from "react";
import ContentContainer from "../components/content-container/content-container";
import HeadingContainer from "../components/heading-container/heading-container";

type ContentProps = {
  users: Maybe<PortalUserQueryResponse>;
};

type UserManagementProps = {
  users: Maybe<PortalUserQueryResponse>;
};

const { small, medium, large } = customGridDefs;

const columns: GridColDef[] = [
  {
    field: "name",
    headerName: "Full Name",
    ...large,
  },
  {
    field: "email",
    headerName: "Email",
    renderCell: ({ row: user }: GridRenderCellParams<PortalUserResponse>) => (
      <a href={`mailto:${user?.email}`}>{user?.email}</a>
    ),
    ...large,
  },
  {
    field: "role",
    headerName: "Role",
    type: "singleSelect",
    valueOptions: [
      { value: OrganizationUserRole.MANAGER, label: OrganizationUserRoleName.MANAGER },
      { value: OrganizationUserRole.TRANSACTOR, label: OrganizationUserRoleName.TRANSACTOR },
      { value: OrganizationUserRole.VIEWER, label: OrganizationUserRoleName.VIEWER },
    ],
    valueGetter: (params: GridValueGetterParams<PortalUserResponse>) => params.row.organizationUserRoles[0],
    ...medium,
  },
  {
    field: "status",
    headerName: "Status",
    type: "singleSelect",
    valueOptions: [
      { value: UserStatus.ENABLED, label: UserStatus.ENABLED },
      { value: UserStatus.PENDING, label: UserStatus.PENDING },
      { value: UserStatus.DISABLED, label: UserStatus.DISABLED },
    ],
    valueGetter: (params: GridValueGetterParams<any, UserStatus>) => params.value,
    renderCell: ({ value }: GridRenderCellParams<any, UserStatus>) => <UserStatusRenderer status={value} />,
    ...small,
  },
];

const Content = ({ users }: ContentProps): JSX.Element => {
  const usersWithActions = useMemo(
    () => users?.data?.filter((user) => user.status === UserStatus.ENABLED)?.map((u) => ({ ...u, actions: [] })),
    [users],
  );

  if (!users?.data?.length) return <></>;

  return (
    <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", ">*": { pt: "10px" } }}>
      <DataTable
        dataGridProps={{
          "aria-label": "Organization users data grid",
          columns,
          rows: usersWithActions ?? [],
          columnBuffer: 9,
        }}
      />
    </Grid>
  );
};

export default function UserManagementPage({ users }: UserManagementProps): JSX.Element {
  return (
    <>
      <HeadingContainer>
        <PageContentHeading headingId="user-management-page-heading" headingText="User Management" />
      </HeadingContainer>
      <ContentContainer>
        <Content users={users} />
      </ContentContainer>
    </>
  );
}

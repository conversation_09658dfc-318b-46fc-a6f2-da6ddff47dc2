import React from "react";
import Typography from "@mui/material/Typography";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import Link from "next/link";
import HomeIcon from "@mui/icons-material/Home";
import classes from "./breadcrumbs.module.scss";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { Route } from "next";

interface Breadcrumb {
  name: string;
  path?: string;
}

interface BreadcrumbsProps {
  home?: string;
  items: Breadcrumb[];
}

export default function BreadCrumbs(props: BreadcrumbsProps): JSX.Element {
  const { home, items } = props;

  const bcItems = items.map((bc: Breadcrumb) => {
    if (bc.path) {
      return (
        <Link key={bc.name} className={classes.navigation} href={bc.path as Route}>
          {bc.name}
        </Link>
      );
    }
    return (
      <Typography key={bc.name} className={classes.navigation} color="text.primary">
        {bc.name}
      </Typography>
    );
  });

  bcItems.unshift(
    <Link key="home" className={classes.navigation} href={(!!home ? home : "/") as any}>
      <HomeIcon sx={{ mr: 0, mt: 1 }} fontSize="inherit" />
    </Link>,
  );

  return (
    <Breadcrumbs separator={<NavigateNextIcon fontSize="large" />} aria-label="breadcrumb">
      {bcItems}
    </Breadcrumbs>
  );
}

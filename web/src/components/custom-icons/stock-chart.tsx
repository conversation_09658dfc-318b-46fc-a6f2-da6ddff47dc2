import { SvgIcon } from "@mui/material";
import { CustomIconProps } from ".";

export const StockChartIcon = (props: CustomIconProps): JSX.Element => {
  const { width, height, htmlColor, ...rest } = props;
  return (
    <SvgIcon {...rest} style={{ width, height }}>
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_8784_22793)">
          <path
            d="M39.2958 37.2016H37.4683V6.89918C37.4683 6.78109 37.4499 6.66415 37.414 6.55504C37.3781 6.44593 37.3254 6.34679 37.259 6.26329C37.1927 6.17979 37.1139 6.11356 37.0272 6.06838C36.9404 6.0232 36.8475 5.99997 36.7536 6H31.9878C31.894 5.99997 31.801 6.02321 31.7143 6.06838C31.6276 6.11356 31.5488 6.17979 31.4824 6.2633C31.4161 6.3468 31.3634 6.44593 31.3275 6.55504C31.2916 6.66415 31.2731 6.78109 31.2732 6.89918V37.2016H27.8875V14.3815C27.8875 14.2634 27.8691 14.1465 27.8332 14.0374C27.7973 13.9283 27.7446 13.8291 27.6782 13.7456C27.6119 13.6621 27.5331 13.5959 27.4464 13.5507C27.3596 13.5055 27.2667 13.4823 27.1728 13.4823H22.407C22.3132 13.4823 22.2202 13.5055 22.1335 13.5507C22.0468 13.5959 21.968 13.6621 21.9016 13.7456C21.8353 13.8291 21.7826 13.9283 21.7467 14.0374C21.7108 14.1465 21.6923 14.2634 21.6924 14.3815V37.2016H18.3067V20.0031C18.3067 19.885 18.2883 19.7681 18.2524 19.659C18.2165 19.5499 18.1638 19.4507 18.0974 19.3672C18.0311 19.2837 17.9523 19.2175 17.8656 19.1723C17.7788 19.1271 17.6859 19.1039 17.592 19.1039H12.8262C12.7324 19.1039 12.6394 19.1271 12.5527 19.1723C12.466 19.2175 12.3872 19.2837 12.3208 19.3672C12.2545 19.4507 12.2018 19.5499 12.1659 19.659C12.13 19.7681 12.1115 19.885 12.1116 20.0031V37.2017H8.72591V26.0524C8.72593 25.9343 8.70747 25.8173 8.67156 25.7082C8.63565 25.5991 8.58301 25.5 8.51664 25.4165C8.45027 25.333 8.37148 25.2668 8.28475 25.2216C8.19804 25.1764 8.10509 25.1532 8.01123 25.1532H3.24543C3.15157 25.1532 3.05862 25.1764 2.9719 25.2216C2.88518 25.2667 2.80639 25.333 2.74002 25.4165C2.67365 25.5 2.62101 25.5991 2.5851 25.7082C2.54919 25.8173 2.53072 25.9343 2.53075 26.0524V37.2016H0.703225C0.515677 37.2055 0.336835 37.3019 0.205278 37.4701C0.0737218 37.6383 -5.62687e-06 37.8649 3.22095e-10 38.1008C5.62751e-06 38.3368 0.0737437 38.5634 0.205308 38.7316C0.336873 38.8998 0.515719 38.9962 0.703268 39C2.25881 38.9979 39.2958 39 39.2958 39C39.4836 38.9965 39.6626 38.9002 39.7944 38.732C39.9261 38.5637 40 38.337 40 38.1008C40 37.8646 39.9261 37.6379 39.7944 37.4697C39.6626 37.3014 39.4836 37.2051 39.2958 37.2016ZM3.96011 37.2016V26.9515H7.29655V37.2016H3.96011ZM13.5409 37.2016V20.9023H16.8774V37.2017L13.5409 37.2016ZM23.1217 37.2016V15.2807H26.4582V37.2016H23.1217ZM32.7025 37.2016V7.79836H36.039V37.2016H32.7025Z"
            fill="white"
            stroke={htmlColor || "white"}
            strokeWidth="0.5"
          />
          <path
            d="M3.71325 16.8877C14.147 16.0278 21.2057 8.89954 24.447 4.72757L24.2147 6.50476C24.1894 6.73252 24.2545 6.9611 24.3959 7.14141C24.5373 7.32173 24.7438 7.4394 24.971 7.46915C25.1982 7.4989 25.428 7.43836 25.6111 7.30053C25.7942 7.1627 25.9159 6.95858 25.9501 6.732L26.65 1.37866C26.6736 1.17063 26.6217 0.961022 26.5038 0.788022C26.3859 0.615022 26.2097 0.490133 26.0074 0.4361C25.911 0.414112 25.813 0.399562 25.7143 0.392578L20.3319 0.812132C20.2171 0.820829 20.1052 0.852069 20.0026 0.904065C19.8999 0.956061 19.8085 1.02779 19.7337 1.11515C19.6588 1.20251 19.6018 1.30378 19.5661 1.41317C19.5304 1.52256 19.5166 1.63792 19.5256 1.75264C19.5345 1.86736 19.566 1.97919 19.6182 2.08174C19.6704 2.18428 19.7423 2.27552 19.8298 2.35023C19.9173 2.42495 20.0187 2.48167 20.1282 2.51715C20.2376 2.55263 20.353 2.56617 20.4677 2.557L24.0649 2.27651C21.6215 5.71745 14.5584 14.1824 3.63768 15.1411C3.41654 15.1652 3.21282 15.2725 3.06789 15.4412C2.92295 15.61 2.84765 15.8276 2.85726 16.0498C2.86688 16.272 2.96069 16.4823 3.11966 16.6379C3.27863 16.7935 3.49085 16.8828 3.71325 16.8877Z"
            fill="white"
            stroke={htmlColor || "white"}
            strokeWidth="0.5"
          />
        </g>
        <defs>
          <clipPath id="clip0_8784_22793">
            <rect width="40" height="39" fill={htmlColor || "white"} />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};

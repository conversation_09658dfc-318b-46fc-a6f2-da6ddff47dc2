import { Maybe } from "@rubiconcarbon/frontend-shared";
import PortfolioName from "./portfolio-name";
import {
  AssetType,
  PortalRctAssetRetirementResponse,
  PortalTransactionResponse,
  PortalAssetResponse,
  uuid,
  PortalVintageAssetRetirementResponse,
} from "@rubiconcarbon/shared-types";
import { Typography } from "@mui/material";
import { CSSProperties } from "react";
import ProjectWithVintageByAsset, { ProjectWithVintage } from "./project-with-vintage";
import { ProductMapping } from "@app/constants/products";

type SingleAssetProductNameProps = {
  asset: PortalRctAssetRetirementResponse | PortalVintageAssetRetirementResponse;
  style?: CSSProperties;
};

type MultiAssetProductNameProps = {
  assets: (PortalRctAssetRetirementResponse | PortalVintageAssetRetirementResponse)[];
  style?: CSSProperties;
};

type ProductNameProps = {
  transaction: PortalTransactionResponse;
  style?: CSSProperties;
};

export const SingleAssetProductName = ({ asset, style = {} }: SingleAssetProductNameProps): JSX.Element => {
  const isPortfolio = asset?.asset?.type === AssetType.RCT;

  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  return (
    <>
      <Maybe condition={isPortfolio}>
        <PortfolioName
          id={asset?.asset?.id}
          name={asset?.asset?.name}
          includeColorChip
          color={ProductMapping?.[asset?.asset?.id]?.color}
          sx={{ fontSize, fontWeight, ...rest }}
        />
      </Maybe>
      <Maybe condition={!isPortfolio}>
        <ProjectWithVintage asset={asset.asset} sx={{ fontSize, fontWeight, ...rest }} />
      </Maybe>
    </>
  );
};

export const MultiAssetProductName = ({ assets, style = {} }: MultiAssetProductNameProps): JSX.Element => {
  const isMultiple = assets?.length > 1;
  const isPortfolio = assets?.some((asset) => asset.asset.type === AssetType.RCT);
  const firstAsset = assets?.at(0);

  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  if (!isMultiple)
    return (
      <>
        <Maybe condition={isPortfolio}>
          <PortfolioName
            id={(firstAsset as PortalRctAssetRetirementResponse)?.asset?.id}
            name={(firstAsset as PortalRctAssetRetirementResponse)?.asset?.name}
            includeColorChip
            color={ProductMapping?.[(firstAsset as PortalRctAssetRetirementResponse)?.asset?.id]?.color}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
        <Maybe condition={!isPortfolio}>
          <ProjectWithVintage
            asset={(firstAsset as PortalVintageAssetRetirementResponse)?.asset}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
      </>
    );
  else return <Typography style={{ fontSize, fontWeight, ...rest }}>Multiple Products</Typography>;
};

const ProductName = ({ transaction, style = {} }: ProductNameProps): JSX.Element => {
  const isMultiple = transaction?.assetFlows?.length > 1;
  const isPortfolio = transaction?.assetFlows?.some(({ asset }) => asset?.type === AssetType.RCT);
  const firstAsset = transaction?.assetFlows?.at(0)?.asset;

  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  if (!isMultiple)
    return (
      <>
        <Maybe condition={isPortfolio}>
          <PortfolioName
            id={firstAsset?.id as uuid}
            name={firstAsset?.name as string}
            includeColorChip
            color={ProductMapping?.[firstAsset?.id as uuid]?.color}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
        <Maybe condition={!isPortfolio}>
          <ProjectWithVintageByAsset asset={firstAsset as PortalAssetResponse} sx={{ fontSize, fontWeight, ...rest }} />
        </Maybe>
      </>
    );
  else return <Typography style={{ fontSize, fontWeight, ...rest }}>Multiple Products</Typography>;
};

export default ProductName;

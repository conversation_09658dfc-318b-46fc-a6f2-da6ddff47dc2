import { Box, Stack, SxProps, Typography } from "@mui/material";
import { px } from "@rubiconcarbon/frontend-shared";
import { PortalAssetResponse } from "@rubiconcarbon/shared-types";
import Link from "next/link";

type ProjectWithVintageProps = {
  asset: PortalAssetResponse;
  linkable?: boolean;
  sx?: SxProps;
};

type ProjectWithVintageByAssetProps = {
  asset: PortalAssetResponse;
  linkable?: boolean;
  sx?: SxProps;
};

export const ProjectWithVintage = ({ asset, linkable = true, sx = {} }: ProjectWithVintageProps): JSX.Element => {
  return (
    <Stack>
      <Box
        component={linkable ? Link : "span"}
        {...px({ href: linkable && `/projects/${asset?.projectId}`, target: linkable && "_blank" })}
        style={{ textUnderlineOffset: 3 }}
      >
        <Typography variant="body2" sx={{ ...sx, overflow: "hidden", textOverflow: "ellipsis", textWrap: "nowrap" }}>
          {asset?.name}
        </Typography>
      </Box>

      <Typography variant="body2" sx={sx} color="GrayText">
        {asset?.registryProjectId} - {asset?.projectVintageName}
      </Typography>
    </Stack>
  );
};

const ProjectWithVintageByAsset = ({
  asset,
  linkable = true,
  sx = {},
}: ProjectWithVintageByAssetProps): JSX.Element => {
  // note that for regular spot vintages, we use the projectId as the id in the asset corresponds to the portfolio entry
  // for forwards, we use the asset id as the id
  return (
    <Stack>
      <Box
        component={linkable ? Link : "span"}
        {...px({ href: linkable && `/projects/${asset?.projectId ?? asset?.id}`, target: linkable && "_blank" })}
        style={{ textUnderlineOffset: 3 }}
      >
        <Typography variant="body2" sx={{ ...sx, overflow: "hidden", textOverflow: "ellipsis", textWrap: "nowrap" }}>
          {asset?.name}
        </Typography>
      </Box>

      <Typography variant="body2" sx={sx} color="GrayText">
        {asset?.registryProjectId} - {asset?.projectVintageName}
      </Typography>
    </Stack>
  );
};

export default ProjectWithVintageByAsset;

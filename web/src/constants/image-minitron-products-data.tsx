import { <PERSON>tron } from "@app/types/static-content";
import { PortalBookResponse } from "@rubiconcarbon/shared-types";
import { BUILD_YOUR_OWN, ProductIds } from "./products";
import { Undefinable } from "@rubiconcarbon/frontend-shared";
import { Stack, Typography } from "@mui/material";
import { Star } from "@mui/icons-material";
import { StaticImageData } from "next/image";

import natureRct from "@assets/images/portfolios/nature-based-rct.webp";
import superpollutantRct from "@assets/images/portfolios/industrial-rct.webp";
import carbonRemovalRct from "@assets/images/portfolios/carbon-removal-rct.webp";
import rubiconRatedTonne from "@assets/images/portfolios/rubicon-rated-tonne.webp";
import byoRct from "@assets/images/portfolios/byo-rct.webp";

type ReconcileOptions = {
  imageOverride?: (book: PortalBookResponse, minitrons?: Minitron) => Undefinable<StaticImageData>;
};

export const reconcileProduct = (
  book: PortalBookResponse,
  minitrons: Minitron[],
  options: ReconcileOptions = {},
): Undefinable<Minitron> => {
  const minitron = minitrons.find((minitron) => minitron.text === book?.id);

  if (!minitron) return undefined;

  return {
    ...(minitron as Minitron),
    link: `/products/${book?.id}`,
    subText: book?.description,
    title: book?.name,
    bottomSlot: book?.purchasePrice?.toString() ?? "",
    image: options.imageOverride?.(book, minitron) ?? minitron.image,
  };
};

export const ImageMinitronProductsData: [Minitron, Minitron, Minitron, Minitron, Minitron] = [
  {
    image: natureRct,
    text: ProductIds.NATURE,
  },
  {
    image: superpollutantRct,
    text: ProductIds.SUPERPOLLUTANT_ELIMINATION,
  },
  {
    image: carbonRemovalRct,
    text: ProductIds.CARBON_REMOVALS,
  },
  {
    image: rubiconRatedTonne,
    text: ProductIds.RUBICON_RATED_TONNE,
    tag: (
      <Stack direction="row" justifyContent="center" alignItems="center" gap={1} width={90}>
        <Star fontSize="small" />
        <Typography fontWeight="bold" fontStyle="italic">
          new
        </Typography>
      </Stack>
    ),
  },
  {
    image: byoRct,
    title: BUILD_YOUR_OWN,
    text: ProductIds.BUILD_YOUR_OWN,
    subText: "Explore our projects and create your bespoke portfolio",
    link: "/products/byo-rct",
  },
];

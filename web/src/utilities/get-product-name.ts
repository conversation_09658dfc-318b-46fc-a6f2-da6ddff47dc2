import { AssetType, PortalTransactionResponse } from "@rubiconcarbon/shared-types";

export const getProductName = (transaction: PortalTransactionResponse): string => {
  const isMultiple = transaction?.assetFlows?.length > 1;
  const isPortfolio = transaction?.assetFlows?.some(({ asset }) => asset?.type === AssetType.RCT);

  if (!isMultiple)
    if (isPortfolio) return transaction?.assetFlows?.at?.(0)?.asset?.name ?? "";
    else {
      const asset = transaction?.assetFlows?.at?.(0)?.asset;
      return `${asset?.name} - (${asset?.registryProjectId} - ${asset?.projectVintageName})`;
    }
  else return "Multiple Products";
};
